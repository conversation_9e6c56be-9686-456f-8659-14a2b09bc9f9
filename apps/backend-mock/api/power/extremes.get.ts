import { useResponseSuccess } from '~/utils/response';

// 电力极值报表数据类型定义
interface PowerExtremesData {
  circuitName: string; // 回路名称
  date: string; // 日期
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: string; // 最大值发生时间
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: string; // 最小值发生时间
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: string; // 最大值发生时间
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: string; // 最小值发生时间
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: string; // 最大值发生时间
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: string; // 最小值发生时间
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: string; // 最大值发生时间
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: string; // 最小值发生时间
  powerFactorAvgValue: number; // 平均值
}

/**
 * 生成随机功率数据
 */
function generateRandomPowerData(baseValue: number, variance: number = 0.3) {
  const maxValue = baseValue * (1 + Math.random() * variance);
  const minValue = baseValue * (1 - Math.random() * variance);
  const avgValue = (maxValue + minValue) / 2;
  
  return {
    maxValue: Number(maxValue.toFixed(2)),
    minValue: Number(minValue.toFixed(2)),
    avgValue: Number(avgValue.toFixed(2)),
    maxTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
    minTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
  };
}

/**
 * 生成随机功率因数数据
 */
function generateRandomPowerFactorData() {
  const baseValue = 0.85 + Math.random() * 0.1; // 0.85-0.95之间
  const maxValue = Math.min(1.0, baseValue + Math.random() * 0.05);
  const minValue = Math.max(0.7, baseValue - Math.random() * 0.1);
  const avgValue = (maxValue + minValue) / 2;
  
  return {
    maxValue: Number(maxValue.toFixed(3)),
    minValue: Number(minValue.toFixed(3)),
    avgValue: Number(avgValue.toFixed(3)),
    maxTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
    minTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
  };
}

/**
 * 生成Mock数据
 */
function generateMockData(count: number = 15): PowerExtremesData[] {
  const circuits = [
    '1#主变高压侧',
    '1#主变低压侧',
    '2#主变高压侧', 
    '2#主变低压侧',
    '1#进线柜',
    '2#进线柜',
    '母联柜',
    '1#出线柜',
    '2#出线柜',
    '3#出线柜',
    '4#出线柜',
    '5#出线柜',
    '备用进线',
    '应急电源',
    'UPS电源',
  ];

  const data: PowerExtremesData[] = [];
  
  for (let i = 0; i < count; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // 生成各种功率数据
    const activePower = generateRandomPowerData(1500, 0.4); // 有功功率基值1500kW
    const reactivePower = generateRandomPowerData(800, 0.5); // 无功功率基值800kVar
    const apparentPower = generateRandomPowerData(1700, 0.3); // 视在功率基值1700kVA
    const powerFactor = generateRandomPowerFactorData(); // 功率因数
    
    data.push({
      circuitName: circuits[i % circuits.length],
      date: date.toISOString().split('T')[0],
      // 有功功率
      activePowerMaxValue: activePower.maxValue,
      activePowerMaxTime: activePower.maxTime,
      activePowerMinValue: activePower.minValue,
      activePowerMinTime: activePower.minTime,
      activePowerAvgValue: activePower.avgValue,
      // 无功功率
      reactivePowerMaxValue: reactivePower.maxValue,
      reactivePowerMaxTime: reactivePower.maxTime,
      reactivePowerMinValue: reactivePower.minValue,
      reactivePowerMinTime: reactivePower.minTime,
      reactivePowerAvgValue: reactivePower.avgValue,
      // 视在功率
      apparentPowerMaxValue: apparentPower.maxValue,
      apparentPowerMaxTime: apparentPower.maxTime,
      apparentPowerMinValue: apparentPower.minValue,
      apparentPowerMinTime: apparentPower.minTime,
      apparentPowerAvgValue: apparentPower.avgValue,
      // 功率因数
      powerFactorMaxValue: powerFactor.maxValue,
      powerFactorMaxTime: powerFactor.maxTime,
      powerFactorMinValue: powerFactor.minValue,
      powerFactorMinTime: powerFactor.minTime,
      powerFactorAvgValue: powerFactor.avgValue,
    });
  }
  
  return data;
}

export default defineEventHandler(async (event) => {
  console.log('Power extremes API called');

  // 获取查询参数
  const query = getQuery(event);
  const { 
    startTime, 
    endTime, 
    reportType = 'dayly',
    circuitName,
    page = 1, 
    pageSize = 50 
  } = query;

  console.log('Query params:', { startTime, endTime, reportType, circuitName, page, pageSize });

  // 生成Mock数据
  let mockData = generateMockData(15);

  // 根据回路名称过滤
  if (circuitName && String(circuitName).trim()) {
    const searchKeyword = String(circuitName).toLowerCase().trim();
    console.log('Filtering circuits with keyword:', searchKeyword);
    mockData = mockData.filter(item => 
      item.circuitName.toLowerCase().includes(searchKeyword)
    );
  }

  // 根据时间范围过滤（简单模拟）
  if (startTime && endTime) {
    console.log('Filtering by date range:', { startTime, endTime });
    // 这里可以根据实际需求实现时间过滤逻辑
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);

  console.log('Mock data length:', mockData.length);
  console.log('Paginated data length:', paginatedData.length);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    reportType,
  });
});
