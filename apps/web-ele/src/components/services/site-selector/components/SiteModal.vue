<script lang="ts" setup>
import type {
  EnterpriseTreeNode,
  RegionTreeNode,
  SiteInfo,
  SiteModalEmits,
  SiteModalProps,
} from '../types';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, nextTick, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { Motion } from '@vben/plugins/motion';

import { IconifyIcon } from '@vben-core/icons';

import { useDebounceFn, useThrottleFn } from '@vueuse/core';
import { ElInput, ElMessage, ElTabPane, ElTabs, ElTree } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import {
  mockEnterpriseTreeData,
  mockRegionTreeData,
  mockSiteData,
  useSiteSearchFormSchema,
  useSiteTableColumns,
} from '../data';

const props = withDefaults(defineProps<SiteModalProps>(), {
  visible: false,
  selectedSite: undefined,
});

const emit = defineEmits<SiteModalEmits>();

// Modal配置
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    emit('cancel');
    modalApi.close();
  },
  onConfirm() {
    handleConfirm();
  },
  onOpenChange(isOpen: boolean) {
    // 当弹窗关闭时（点击遮罩、ESC等），同步父组件状态
    if (!isOpen) {
      emit('cancel');
    }
  },
});

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      modalApi.open();
      // Modal打开时初始化默认选择
      nextTick(() => {
        setTimeout(() => {
          initializeDefaultSelection();
        }, 200); // 延迟确保DOM已渲染和数据已准备
      });
    } else {
      modalApi.close();
    }
  },
);

// 左侧标签页
const activeTab = ref('enterprise');
const enterpriseSearchKeyword = ref('');
const regionSearchKeyword = ref('');

// 临时搜索输入（用于防抖）
const tempEnterpriseSearch = ref('');
const tempRegionSearch = ref('');

// 树形控件引用
const enterpriseTreeRef = ref<InstanceType<typeof ElTree>>();
const regionTreeRef = ref<InstanceType<typeof ElTree>>();

// 选中的树节点
const selectedEnterpriseNode = ref<string>('');
const selectedRegionNode = ref<string>('');

// 防抖搜索函数
const debouncedEnterpriseSearch = useDebounceFn((keyword: string) => {
  enterpriseSearchKeyword.value = keyword;
}, 300);

const debouncedRegionSearch = useDebounceFn((keyword: string) => {
  regionSearchKeyword.value = keyword;
}, 300);

// 防抖表格查询函数
const debouncedGridQuery = useDebounceFn(() => {
  if (gridApi && gridApi.query) {
    gridApi.query();
  }
}, 500);

// 监听临时搜索输入，触发防抖搜索
watch(tempEnterpriseSearch, (newValue) => {
  debouncedEnterpriseSearch(newValue);
});

watch(tempRegionSearch, (newValue) => {
  debouncedRegionSearch(newValue);
});

// 节流处理窗口大小变化时的表格重新计算
const throttledTableResize = useThrottleFn(() => {
  if (gridApi && gridApi.recalculate) {
    gridApi.recalculate();
  }
}, 200);

// 过滤后的树形数据
const filteredEnterpriseData = computed(() => {
  if (!enterpriseSearchKeyword.value) return mockEnterpriseTreeData;
  return mockEnterpriseTreeData.filter((item) =>
    item.label.includes(enterpriseSearchKeyword.value),
  );
});

const filteredRegionData = computed(() => {
  if (!regionSearchKeyword.value) return mockRegionTreeData;

  const filterTree = (nodes: RegionTreeNode[]): RegionTreeNode[] => {
    return nodes.reduce((acc: RegionTreeNode[], node) => {
      const matchesKeyword = node.label.includes(regionSearchKeyword.value);
      const filteredChildren = node.children ? filterTree(node.children) : [];

      if (matchesKeyword || filteredChildren.length > 0) {
        acc.push({
          ...node,
          children:
            filteredChildren.length > 0 ? filteredChildren : node.children,
        });
      }
      return acc;
    }, []);
  };

  return filterTree(mockRegionTreeData);
});

// 监听企业数据变化，确保有数据时进行默认选择
watch(
  () => filteredEnterpriseData.value,
  (newData) => {
    if (
      newData &&
      newData.length > 0 &&
      activeTab.value === 'enterprise' &&
      !selectedEnterpriseNode.value
    ) {
      setTimeout(() => {
        initializeDefaultSelection();
      }, 50);
    }
  },
  { immediate: false }, // 改为false，避免初始化时的问题
);

// 初始化默认选择
const initializeDefaultSelection = () => {
  // TODO: 这里将来可以从props或存储中获取先前已经勾选设置过的值
  // const previouslySelected = props.previouslySelectedEnterprise;

  try {
    if (
      activeTab.value === 'enterprise' &&
      filteredEnterpriseData.value &&
      filteredEnterpriseData.value.length > 0 &&
      !selectedEnterpriseNode.value
    ) {
      // 暂时忽略先前已经勾选设置过的值，默认选择第一项
      const firstEnterprise = filteredEnterpriseData.value[0];
      selectedEnterpriseNode.value = firstEnterprise.value;

      // 设置树形控件的选中状态
      setTimeout(() => {
        if (enterpriseTreeRef.value) {
          enterpriseTreeRef.value.setCheckedKeys([firstEnterprise.value]);
        }
      }, 100);

      console.log('默认选中企业:', firstEnterprise.label);

      // 防抖刷新表格数据
      setTimeout(() => {
        debouncedGridQuery();
      }, 150);
    }
  } catch (error) {
    console.warn('初始化默认选择时出错:', error);
  }
};

// VXE表格事件配置
const gridEvents = {
  currentRowChange: handleRowClick,  // 当前行变化事件
  cellClick: handleRowClick,         // 单元格点击事件（作为备选）
};

// VXE表格配置
const [Grid, gridApi] = useVbenVxeGrid({
  gridEvents,
  formOptions: {
    collapsed: false,
    commonConfig: {
      componentProps: {
        class: 'w-full',
      },
    },
    schema: useSiteSearchFormSchema(),
    showCollapseButton: false,
    submitOnChange: false,
    submitOnEnter: true,
    layout: 'horizontal',
  },
  showSearchForm: true,
  gridOptions: {
    ...createGridOptions(),
    proxyConfig: {
      autoLoad: true,
      ajax: {
        query: async (params: any, formValues: any) => {
          console.log('站点查询参数:', { params, formValues });

          let filteredData = [...mockSiteData];

          // 根据搜索条件过滤
          if (formValues?.siteType) {
            filteredData = filteredData.filter(
              (site) => site.type === formValues.siteType,
            );
          }

          if (formValues?.keyword) {
            filteredData = filteredData.filter(
              (site) =>
                site.name.includes(formValues.keyword) ||
                site.enterprise.includes(formValues.keyword) ||
                site.region.includes(formValues.keyword) ||
                site.address.includes(formValues.keyword),
            );
          }

          // 根据左侧选中的树节点过滤
          if (
            activeTab.value === 'enterprise' &&
            selectedEnterpriseNode.value
          ) {
            const selectedNode = mockEnterpriseTreeData.find(
              (node) => node.value === selectedEnterpriseNode.value,
            );
            if (selectedNode) {
              filteredData = filteredData.filter(
                (site) => site.enterprise === selectedNode.label,
              );
              console.log(
                `按企业过滤: ${selectedNode.label}, 结果数量: ${filteredData.length}`,
              );
            }
          }

          if (activeTab.value === 'region' && selectedRegionNode.value) {
            // 根据区域节点进行过滤
            const selectedNode = findRegionNodeByValue(
              selectedRegionNode.value,
            );
            if (selectedNode) {
              filteredData = filteredData.filter((site) => {
                // 如果选择的是"全部"，显示所有数据
                if (selectedNode.value === 'all') {
                  return true;
                }
                // 如果选择的是省级节点，匹配省份
                if (
                  selectedNode.value === 'beijing' ||
                  selectedNode.value === 'hebei'
                ) {
                  return site.region.includes(
                    selectedNode.label.replace('市', '').replace('省', ''),
                  );
                }
                // 如果选择的是市级节点，精确匹配
                return site.region.includes(
                  selectedNode.label.replace('区', '').replace('市', ''),
                );
              });
              console.log(
                `按区域过滤: ${selectedNode.label}, 结果数量: ${filteredData.length}`,
              );
            }
          }

          return {
            items: filteredData,
            total: filteredData.length,
          };
        },
      },
    },
  },
});

// 当前选中的站点
const currentSelectedSite = ref<null | SiteInfo>(null);

// 辅助函数：递归查找区域节点
const findRegionNodeByValue = (value: string): null | RegionTreeNode => {
  const searchInNodes = (nodes: RegionTreeNode[]): null | RegionTreeNode => {
    for (const node of nodes) {
      if (node.value === value) {
        return node;
      }
      if (node.children) {
        const found = searchInNodes(node.children);
        if (found) return found;
      }
    }
    return null;
  };
  return searchInNodes(mockRegionTreeData);
};

// 创建基础表格配置
function createGridOptions(): VxeTableGridOptions<SiteInfo> {
  return {
    columns: useSiteTableColumns(),
    height: 'auto',
    keepSource: true,
    autoResize: true,
    columnConfig: {
      resizable: true,
      useKey: true,
    },
    border: true,
    stripe: true,
    // round: true,
    pagerConfig: {
      enabled: false,
    },
    rowConfig: {
      keyField: 'id',
      isCurrent: true,
      isHover: true,
    },
  };
}

// 企业选择统一处理函数
const handleEnterpriseSelection = (
  data: EnterpriseTreeNode,
  isChecked: boolean,
) => {
  const tree = enterpriseTreeRef.value;
  if (tree) {
    if (isChecked) {
      // 单选模式：只选中当前节点
      tree.setCheckedKeys([data.value]);
      selectedEnterpriseNode.value = data.value;
      console.log('选中企业:', data.label);
    } else {
      // 取消选中
      tree.setCheckedKeys([]);
      selectedEnterpriseNode.value = '';
      console.log('取消选中企业:', data.label);
    }
    // 防抖刷新表格数据
    debouncedGridQuery();
  }
};

// 树节点点击事件
const handleEnterpriseNodeClick = (data: EnterpriseTreeNode) => {
  const tree = enterpriseTreeRef.value;
  if (tree) {
    const checkedKeys = tree.getCheckedKeys();
    const isCurrentlyChecked = checkedKeys.includes(data.value);

    if (isCurrentlyChecked) {
      // 如果点击的是已选中的企业，保持选中状态（不取消）
      console.log('企业已选中:', data.label);
    } else {
      // 如果点击的是未选中的企业，选中它
      handleEnterpriseSelection(data, true);
    }
  }
};

// 企业树形控件checkbox选择事件
const handleEnterpriseNodeCheck = (
  data: EnterpriseTreeNode,
  checkState: any,
) => {
  const isChecked = checkState.checkedKeys.includes(data.value);
  handleEnterpriseSelection(data, isChecked);
};

// 区域选择统一处理函数
const handleRegionSelection = (data: RegionTreeNode, isChecked: boolean) => {
  const tree = regionTreeRef.value;
  if (tree) {
    if (isChecked) {
      // 单选模式：只选中当前节点
      tree.setCheckedKeys([data.value]);
      selectedRegionNode.value = data.value;
      console.log('选中区域:', data.label);
    } else {
      // 取消选中
      tree.setCheckedKeys([]);
      selectedRegionNode.value = '';
      console.log('取消选中区域:', data.label);
    }
    // 防抖刷新表格数据
    debouncedGridQuery();
  }
};

// 区域树节点点击事件
const handleRegionNodeClick = (data: RegionTreeNode) => {
  const tree = regionTreeRef.value;
  if (tree) {
    const checkedKeys = tree.getCheckedKeys();
    const isCurrentlyChecked = checkedKeys.includes(data.value);

    if (isCurrentlyChecked) {
      // 如果点击的是已选中的区域，保持选中状态（不取消）
      console.log('区域已选中:', data.label);
    } else {
      // 如果点击的是未选中的区域，选中它
      handleRegionSelection(data, true);
    }
  }
};

// 区域树形控件checkbox选择事件
const handleRegionNodeCheck = (data: RegionTreeNode, checkState: any) => {
  const isChecked = checkState.checkedKeys.includes(data.value);
  handleRegionSelection(data, isChecked);
};

// 表格行点击事件处理函数
function handleRowClick(params: any) {
  // currentRowChange 事件参数结构: { row, rowIndex, $rowIndex }
  // cellClick 事件参数结构: { row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }
  const row = params.row as SiteInfo;
  if (row) {
    console.log('选中站点:', row);
    currentSelectedSite.value = row;
    emit('siteSelected', row);
  }
}

// 确认选择
const handleConfirm = () => {
  if (!currentSelectedSite.value) {
    ElMessage.warning('请选择一个站点');
    return;
  }

  emit('confirm', currentSelectedSite.value);
  modalApi.close();
};

// 标签页切换
const handleTabChange = (tabName: number | string) => {
  activeTab.value = String(tabName);
  // 清空之前的选择
  selectedEnterpriseNode.value = '';
  selectedRegionNode.value = '';

  // 清空树形控件的选中状态
  if (enterpriseTreeRef.value) {
    enterpriseTreeRef.value.setCheckedKeys([]);
  }
  if (regionTreeRef.value) {
    regionTreeRef.value.setCheckedKeys([]);
    regionTreeRef.value.setCurrentKey(null);
  }

  // 防抖刷新表格
  debouncedGridQuery();
};
</script>

<template>
  <Modal
    class="w-[1200px]"
    title="站点选择"
    confirm-text="确定"
    cancel-text="取消"
    content-class="overflow-hidden site-selector-modal"
  >
    <Motion preset="fade" :duration="300" :delay="0">
      <div class="flex h-[60vh] w-full gap-4">
        <!-- 左侧：标签页 + 树形控件 -->
        <div class="w-[300px]">
          <ElTabs
            v-model="activeTab"
            @tab-change="handleTabChange"
            type="border-card"
            class="site-selector-tabs h-full"
          >
            <ElTabPane label="企业" name="enterprise">
              <div class="space-y-3 p-4">
                <!-- 企业搜索框 -->
                <ElInput
                  v-model="tempEnterpriseSearch"
                  placeholder="请输入企业名称"
                  clearable
                  class="h-[40px] w-full"
                >
                  <template #suffix>
                    <IconifyIcon
                      icon="lucide:search"
                      class="h-4 w-4 text-gray-400"
                    />
                  </template>
                </ElInput>

                <!-- 企业树形控件 -->
                <div class="h-[450px] overflow-auto">
                  <ElTree
                    ref="enterpriseTreeRef"
                    :data="filteredEnterpriseData"
                    :props="{ children: 'children', label: 'label' }"
                    node-key="value"
                    :highlight-current="true"
                    :expand-on-click-node="false"
                    :check-strictly="true"
                    show-checkbox
                    :check-on-click-node="true"
                    @node-click="handleEnterpriseNodeClick"
                    @check="handleEnterpriseNodeCheck"
                    class="w-full"
                  />
                </div>
              </div>
            </ElTabPane>

            <ElTabPane label="区域" name="region">
              <div class="space-y-3 p-4">
                <!-- 区域搜索框 -->
                <ElInput
                  v-model="tempRegionSearch"
                  placeholder="请输入区域名称"
                  clearable
                  class="h-[40px] w-full"
                >
                  <template #suffix>
                    <IconifyIcon
                      icon="lucide:search"
                      class="h-4 w-4 text-gray-400"
                    />
                  </template>
                </ElInput>

                <!-- 区域树形控件 -->
                <div class="h-[450px] overflow-auto">
                  <ElTree
                    ref="regionTreeRef"
                    :data="filteredRegionData"
                    :props="{ children: 'children', label: 'label' }"
                    node-key="value"
                    :highlight-current="true"
                    :expand-on-click-node="false"
                    :check-strictly="true"
                    :default-expand-all="true"
                    show-checkbox
                    :check-on-click-node="true"
                    @node-click="handleRegionNodeClick"
                    @check="handleRegionNodeCheck"
                    class="w-full"
                  />
                </div>
              </div>
            </ElTabPane>
          </ElTabs>
        </div>

        <!-- 右侧：VXE表格 -->
        <div class="flex-1">
          <Grid />
        </div>
      </div>
    </Motion>
  </Modal>
</template>

<style scoped>
/* ElTabs 样式优化 - 参考企业能源报表 */
.site-selector-tabs {
  @apply h-full;
}

.site-selector-tabs :deep(.el-tabs__nav-wrap) {
  @apply bg-background border-border border-b;
}

.site-selector-tabs :deep(.el-tabs__nav) {
  @apply bg-background;
}

.site-selector-tabs :deep(.el-tabs__item) {
  @apply text-foreground transition-colors duration-200;
}

.site-selector-tabs :deep(.el-tabs__item:hover) {
  @apply text-primary;
}

.site-selector-tabs :deep(.el-tabs__item.is-active) {
  @apply text-primary font-medium;
}

/* 标签页内容区域 */
.site-selector-tabs :deep(.el-tabs__content) {
  @apply h-full overflow-hidden;
}

.site-selector-tabs :deep(.el-tab-pane) {
  @apply h-full;
}

:deep(.el-tree) {
  background: transparent;
}

/* 树形控件样式 */
:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
  @apply transition-colors duration-200;
}

:deep(.el-tree-node__content:hover) {
  @apply bg-accent/50;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  @apply bg-primary/10 text-primary font-medium;
}

/* VXE表格样式 */
:deep(.vxe-table .vxe-body--row.row--current) {
  background-color: hsl(var(--primary) / 0.1);
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  @apply transition-all duration-200;
}

:deep(.el-input__wrapper:hover) {
  @apply border-primary/50;
}

:deep(.el-input__wrapper.is-focus) {
  @apply border-primary shadow-sm;
}

:deep(.el-tabs--border-card.site-selector-tabs) {
  background: hsl(var(--card));
  border: none !important;
}
:root[data-theme='tech-blue'].dark
  .site-selector-modal
  .bg-card:deep(.vxe-grid) {
  height: 100% !important;
}
</style>
