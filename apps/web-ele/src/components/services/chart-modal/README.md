# 图表按钮组件 (ChartButton)

基于 VbenModal 封装的图表按钮组件，点击按钮弹出包含图表和配置的弹窗。

## 功能特性

- 🎯 **按钮触发**：点击按钮自动打开图表弹窗
- 📊 **图表展示**：ECharts 实现的负载率曲线图
- 📅 **日期控制**：支持日期选择和前后日切换
- 🔧 **显示控制**：连线/数据显示开关
- 📈 **指标切换**：支持多种指标类型选择
- 🎨 **响应式设计**：适配不同屏幕尺寸
- ⚡ **程序化控制**：支持外部控制弹窗开关

## 组件结构

```
chart-modal/
├── index.vue                 # 主组件入口（按钮组件）
├── types.ts                  # TypeScript 类型定义
├── data.ts                   # 数据配置和 Mock 数据
├── components/
│   └── ChartModal.vue        # Modal 内容组件
├── example.vue               # 使用示例
└── README.md                 # 说明文档
```

## 基础使用

```vue
<template>
  <ChartButton
    text="查看负载率曲线"
    chart-title="负载率分析"
    chart-type="loadRate"
    @click="handleButtonClick"
    @chart-opened="handleChartOpened"
    @chart-closed="handleChartClosed"
  />
</template>

<script setup>
import ChartButton from '#/components/services/chart-modal/index.vue';

const handleButtonClick = () => {
  console.log('按钮被点击');
};

const handleChartOpened = () => {
  console.log('图表弹窗已打开');
};

const handleChartClosed = () => {
  console.log('图表弹窗已关闭');
};
</script>
```

## Props 配置

### ChartButton Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | string | '执行操作' | 按钮文本 |
| size | 'small' \| 'default' \| 'large' | 'default' | 按钮大小 |
| disabled | boolean | false | 是否禁用 |
| buttonClass | string | '' | 按钮自定义类名 |
| chartTitle | string | '负载率曲线' | 图表弹窗标题 |
| chartType | 'loadRate' \| 'power' \| 'temperature' | 'loadRate' | 图表类型 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | - | 按钮点击事件 |
| chart-opened | - | 图表弹窗打开事件 |
| chart-closed | - | 图表弹窗关闭事件 |

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| openChart | - | void | 程序化打开图表弹窗 |
| closeChart | - | void | 程序化关闭图表弹窗 |

## 图表功能

### 顶部工具栏
- **日期选择器**：选择查看的日期
- **前后日切换**：快速切换到前一天/后一天
- **连线按钮**：控制图表是否显示面积填充
- **数据按钮**：控制图表是否显示数据点
- **指标选择**：切换不同的监控指标

### 图表特性
- **时间轴**：24小时时间轴显示
- **数据缩放**：底部滑块支持数据范围缩放
- **标记线**：显示最大值和最小值标记
- **工具提示**：鼠标悬停显示详细数据
- **响应式**：自适应容器大小

## 使用示例

### 基础用法
```vue
<ChartButton text="查看图表" />
```

### 自定义配置
```vue
<ChartButton
  text="负载率分析"
  chart-title="10kV变压器负载率"
  chart-type="loadRate"
  size="large"
  button-class="bg-blue-500 text-white"
/>
```

### 程序化控制
```vue
<template>
  <div>
    <ChartButton ref="chartRef" text="图表" />
    <button @click="openChart">打开</button>
    <button @click="closeChart">关闭</button>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const chartRef = ref();

const openChart = () => {
  chartRef.value?.openChart();
};

const closeChart = () => {
  chartRef.value?.closeChart();
};
</script>
```

## 图表类型

### loadRate - 负载率
- 单位：%
- 范围：0-100
- 颜色：#00d4aa

### power - 有功功率
- 单位：kW
- 范围：0-500
- 颜色：#10b981

### temperature - 绕组温度
- 单位：°C
- 范围：40-90
- 颜色：#eab308

## 注意事项

1. 组件依赖 VbenModal、Element Plus 和 ECharts
2. 需要在项目中正确配置 Tailwind CSS
3. Mock 数据仅用于演示，生产环境需要接入真实API
4. 图表会在弹窗打开300ms后渲染，确保DOM已准备就绪
5. 支持主题切换，图表颜色会自动适配

## 测试页面

组件已创建完成，可以通过以下方式测试：

1. **演示页面**：`apps/web-ele/src/components/services/chart-modal/example.vue`
2. **集成使用**：在变压器监控页面替换原有的 PrimaryButton
