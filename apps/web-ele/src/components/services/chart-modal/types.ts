export interface ChartModalProps {
  /** 是否显示Modal */
  visible: boolean;
  /** 图表标题 */
  title?: string;
  /** 图表类型 */
  chartType?: 'loadRate' | 'power' | 'temperature';
}

export interface ChartModalEmits {
  'update:visible': [visible: boolean];
  confirm: [];
  cancel: [];
}

export interface ChartButtonProps {
  /** 按钮文本 */
  text?: string;
  /** 按钮大小 */
  size?: 'small' | 'default' | 'large';
  /** 是否禁用 */
  disabled?: boolean;
  /** 按钮类名 */
  buttonClass?: string;
  /** 图表标题 */
  chartTitle?: string;
  /** 图表类型 */
  chartType?: 'loadRate' | 'power' | 'temperature';
}

export interface ChartButtonEmits {
  click: [];
  'chart-opened': [];
  'chart-closed': [];
}

export interface ChartData {
  time: string;
  value: number;
  max?: number;
  min?: number;
}

export interface ChartConfig {
  title: string;
  unit: string;
  color: string;
  yAxisMax: number;
  yAxisMin: number;
}
