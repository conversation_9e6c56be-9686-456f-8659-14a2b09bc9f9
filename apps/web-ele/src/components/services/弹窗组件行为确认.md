# 弹窗组件行为确认与修复

## VbenModal 默认配置分析

根据源码 `packages/@core/ui-kit/popup-ui/src/modal/modal-api.ts` 第 38-62 行：

```typescript
const defaultState: ModalState = {
  bordered: true,
  centered: false,
  class: '',
  closeOnClickModal: true,     // ✅ 默认支持点击外部关闭
  closeOnPressEscape: true,    // ✅ 默认支持 ESC 关闭
  confirmDisabled: false,
  confirmLoading: false,
  contentClass: '',
  destroyOnClose: true,        // ⚠️ 默认销毁组件
  draggable: false,
  footer: true,
  footerClass: '',
  fullscreen: false,
  fullscreenButton: true,
  header: true,
  headerClass: '',
  isOpen: false,
  loading: false,
  modal: true,
  openAutoFocus: false,
  showCancelButton: true,
  showConfirmButton: true,
  title: '',
};
```

## 当前组件实现分析

### 1. SiteSelector 组件

**配置**：
```typescript
// apps/web-ele/src/components/services/site-selector/components/SiteModal.vue
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    emit('cancel');
    modalApi.close();
  },
  onConfirm() {
    handleConfirm();
  },
});
```

**使用默认配置**：
- `closeOnClickModal: true` - ✅ 支持点击外部关闭
- `destroyOnClose: true` - ⚠️ 弹窗关闭时销毁组件

### 2. ChartButton 组件

**配置**：
```typescript
// apps/web-ele/src/components/services/chart-modal/components/ChartModal.vue
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    emit('cancel');
    modalApi.close();
  },
  onConfirm() {
    emit('confirm');
    modalApi.close();
  },
});
```

**使用默认配置**：
- `closeOnClickModal: true` - ✅ 支持点击外部关闭
- `destroyOnClose: true` - ⚠️ 弹窗关闭时销毁组件

## 预期行为确认

### ✅ 问题1：点击弹窗外部区域时弹窗可以关闭
**结论**：**应该可以正常关闭**
- VbenModal 默认 `closeOnClickModal: true`
- 两个组件都没有覆盖这个配置
- 理论上点击外部应该可以关闭弹窗

### ⚠️ 问题2：弹窗关闭后组件被销毁
**结论**：**组件会被销毁**
- VbenModal 默认 `destroyOnClose: true`
- 两个组件都没有覆盖这个配置
- 弹窗关闭后组件确实会被销毁

### ❌ 问题3：点击空白处关闭弹窗后，再次触发可能无法正常显示
**结论**：**可能存在问题**
- 由于 `destroyOnClose: true`，组件被销毁后需要重新创建
- 如果状态管理有问题，可能导致再次打开失败

## 销毁机制分析

根据 `packages/@core/ui-kit/popup-ui/src/modal/use-modal.ts` 第 97-103 行：

```typescript
const onClosed = mergedOptions.onClosed;
mergedOptions.onClosed = () => {
  onClosed?.();
  if (mergedOptions.destroyOnClose) {
    injectData.reCreateModal?.();  // 重新创建 Modal
  }
};
```

**销毁流程**：
1. 弹窗关闭动画结束
2. 调用 `onClosed` 回调
3. 如果 `destroyOnClose: true`，调用 `reCreateModal()` 重新创建组件

## 测试方法

### 测试页面
1. **SiteSelector**: http://localhost:5779/examples/site-selector
2. **ChartButton**: http://localhost:5779/examples/chart-button
3. **变压器监控**: http://localhost:5779/municipal-power/transformer-monitoring

### 测试步骤

#### 测试1：点击外部关闭
1. 点击按钮打开弹窗
2. 点击弹窗外部的遮罩区域
3. **预期**：弹窗应该关闭

#### 测试2：组件销毁确认
1. 打开浏览器开发者工具
2. 点击按钮打开弹窗
3. 在弹窗中进行一些操作（如选择站点、修改图表配置）
4. 点击外部关闭弹窗
5. 再次打开弹窗
6. **预期**：之前的操作状态应该丢失（因为组件被销毁）

#### 测试3：再次打开问题
1. 点击按钮打开弹窗
2. 点击外部关闭弹窗
3. 立即再次点击按钮
4. **预期**：弹窗应该正常打开
5. **可能问题**：如果状态同步有问题，可能无法打开

### 调试方法

#### 1. 添加临时调试日志
在组件中添加 console.log 来追踪状态：

```typescript
// 在 watch 中添加
watch(
  () => props.visible,
  (newVal) => {
    console.log('Modal visible changed to:', newVal);
    if (newVal) {
      console.log('Opening modal...');
      modalApi.open();
    } else {
      console.log('Closing modal...');
      modalApi.close();
    }
  },
);
```

#### 2. 检查 Vue DevTools
- 查看组件树中的弹窗组件是否被销毁和重新创建
- 观察 props 和 state 的变化

#### 3. 网络面板
- 检查是否有重复的网络请求（组件重新创建时可能触发）

## 可能的问题原因

### 1. 状态同步问题
- 父组件的 `visible` 状态与弹窗的实际状态不同步
- 点击外部关闭时，父组件的 `visible` 可能没有正确更新

### 2. 组件销毁时机问题
- `destroyOnClose: true` 导致组件销毁
- 重新创建组件时可能存在时机问题

### 3. 事件处理问题
- 点击外部关闭时，可能没有正确触发父组件的状态更新
- `onCancel` 回调可能没有被正确调用

## 预期测试结果

### 正常情况
- ✅ 点击外部可以关闭弹窗
- ✅ 弹窗关闭后组件被销毁（状态丢失）
- ✅ 再次点击可以正常打开弹窗

### 可能的问题
- ❌ 点击外部关闭后，父组件 `visible` 状态没有更新
- ❌ 再次点击时，由于状态问题导致无法打开
- ❌ 组件销毁和重新创建过程中出现异常

## 问题确认与修复

### 🔍 问题确认
通过 Vue DevTools 调试发现：**点击遮罩关闭时 SiteSelector 的 modalVisible 没有更改过来**

### 🎯 问题根源
VbenModal 的点击遮罩关闭机制：
```typescript
// packages/@core/ui-kit/popup-ui/src/modal/modal.vue 第 201 行
@update:open="() => (!submitting ? modalApi?.close() : undefined)"
```

**问题流程**：
1. 点击遮罩 → Dialog 触发 `@update:open` 事件
2. 调用 `modalApi?.close()` → 只更新 VbenModal 内部状态
3. **❌ 没有调用 `onCancel` 回调** → 父组件状态不同步
4. 结果：VbenModal 关闭，但父组件 `modalVisible` 仍为 `true`

### ✅ 修复方案（已应用）
使用方案一：添加 `onOpenChange` 回调来同步状态

#### SiteSelector 修复：
```typescript
// apps/web-ele/src/components/services/site-selector/components/SiteModal.vue
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    emit('cancel');
    modalApi.close();
  },
  onConfirm() {
    handleConfirm();
  },
  onOpenChange(isOpen: boolean) {
    // 当弹窗关闭时（点击遮罩、ESC等），同步父组件状态
    if (!isOpen) {
      emit('cancel');
    }
  },
});
```

#### ChartButton 修复：
```typescript
// apps/web-ele/src/components/services/chart-modal/components/ChartModal.vue
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    emit('cancel');
    modalApi.close();
  },
  onConfirm() {
    emit('confirm');
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    // 当弹窗关闭时（点击遮罩、ESC等），同步父组件状态
    if (!isOpen) {
      emit('cancel');
    }
  },
});
```

### 🧪 修复后测试
现在可以测试以下场景：
1. ✅ 点击遮罩关闭 → 父组件状态正确同步
2. ✅ 再次点击打开 → 弹窗正常显示
3. ✅ ESC 键关闭 → 状态正确同步
4. ✅ 按钮关闭 → 状态正确同步

### 📝 修复效果
- **解决状态同步问题**：点击遮罩关闭时父组件状态正确更新
- **保持原有功能**：所有关闭方式都能正确同步状态
- **向后兼容**：不影响现有的按钮关闭逻辑
- **统一行为**：所有关闭方式都通过 `emit('cancel')` 统一处理
