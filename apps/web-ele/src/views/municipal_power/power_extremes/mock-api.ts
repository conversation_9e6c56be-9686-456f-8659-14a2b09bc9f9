import type { PowerExtremesData } from './data';

/**
 * 生成随机功率数据
 */
function generateRandomPowerData(baseValue: number, variance: number = 0.3) {
  const maxValue = baseValue * (1 + Math.random() * variance);
  const minValue = baseValue * (1 - Math.random() * variance);
  const avgValue = (maxValue + minValue) / 2;
  
  return {
    maxValue: Number(maxValue.toFixed(2)),
    minValue: Number(minValue.toFixed(2)),
    avgValue: Number(avgValue.toFixed(2)),
    maxTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
    minTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
  };
}

/**
 * 生成随机功率因数数据
 */
function generateRandomPowerFactorData() {
  const baseValue = 0.85 + Math.random() * 0.1; // 0.85-0.95之间
  const maxValue = Math.min(1.0, baseValue + Math.random() * 0.05);
  const minValue = Math.max(0.7, baseValue - Math.random() * 0.1);
  const avgValue = (maxValue + minValue) / 2;
  
  return {
    maxValue: Number(maxValue.toFixed(3)),
    minValue: Number(minValue.toFixed(3)),
    avgValue: Number(avgValue.toFixed(3)),
    maxTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
    minTime: `${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
  };
}

/**
 * 生成Mock数据
 */
export function generateMockData(count: number = 10): PowerExtremesData[] {
  const circuits = [
    '1#主变高压侧',
    '1#主变低压侧',
    '2#主变高压侧', 
    '2#主变低压侧',
    '1#进线柜',
    '2#进线柜',
    '母联柜',
    '1#出线柜',
    '2#出线柜',
    '3#出线柜',
  ];

  const data: PowerExtremesData[] = [];
  
  for (let i = 0; i < count; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // 生成各种功率数据
    const activePower = generateRandomPowerData(1500, 0.4); // 有功功率基值1500kW
    const reactivePower = generateRandomPowerData(800, 0.5); // 无功功率基值800kVar
    const apparentPower = generateRandomPowerData(1700, 0.3); // 视在功率基值1700kVA
    const powerFactor = generateRandomPowerFactorData(); // 功率因数
    
    data.push({
      circuitName: circuits[i % circuits.length],
      date: date.toISOString().split('T')[0],
      // 有功功率
      activePowerMaxValue: activePower.maxValue,
      activePowerMaxTime: activePower.maxTime,
      activePowerMinValue: activePower.minValue,
      activePowerMinTime: activePower.minTime,
      activePowerAvgValue: activePower.avgValue,
      // 无功功率
      reactivePowerMaxValue: reactivePower.maxValue,
      reactivePowerMaxTime: reactivePower.maxTime,
      reactivePowerMinValue: reactivePower.minValue,
      reactivePowerMinTime: reactivePower.minTime,
      reactivePowerAvgValue: reactivePower.avgValue,
      // 视在功率
      apparentPowerMaxValue: apparentPower.maxValue,
      apparentPowerMaxTime: apparentPower.maxTime,
      apparentPowerMinValue: apparentPower.minValue,
      apparentPowerMinTime: apparentPower.minTime,
      apparentPowerAvgValue: apparentPower.avgValue,
      // 功率因数
      powerFactorMaxValue: powerFactor.maxValue,
      powerFactorMaxTime: powerFactor.maxTime,
      powerFactorMinValue: powerFactor.minValue,
      powerFactorMinTime: powerFactor.minTime,
      powerFactorAvgValue: powerFactor.avgValue,
    });
  }
  
  return data;
}

/**
 * 模拟API调用
 */
export async function fetchPowerExtremesData(params: {
  startTime?: string;
  endTime?: string;
  reportType?: string;
}): Promise<{ data: PowerExtremesData[]; total: number }> {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const mockData = generateMockData(15);
  
  return {
    data: mockData,
    total: mockData.length,
  };
}
