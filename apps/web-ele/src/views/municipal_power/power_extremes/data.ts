import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

// 报表类型定义
export type ReportType = 'custom' | 'dayly' | 'monthly';

// 电力极值报表数据类型定义
export interface PowerExtremesData {
  circuitName: string; // 回路名称
  date: string; // 日期
  // 有功功率(kW)
  activePowerMaxValue: number; // 最大值数值
  activePowerMaxTime: string; // 最大值发生时间
  activePowerMinValue: number; // 最小值数值
  activePowerMinTime: string; // 最小值发生时间
  activePowerAvgValue: number; // 平均值
  // 无功功率(kVar)
  reactivePowerMaxValue: number; // 最大值数值
  reactivePowerMaxTime: string; // 最大值发生时间
  reactivePowerMinValue: number; // 最小值数值
  reactivePowerMinTime: string; // 最小值发生时间
  reactivePowerAvgValue: number; // 平均值
  // 视在功率(kVA)
  apparentPowerMaxValue: number; // 最大值数值
  apparentPowerMaxTime: string; // 最大值发生时间
  apparentPowerMinValue: number; // 最小值数值
  apparentPowerMinTime: string; // 最小值发生时间
  apparentPowerAvgValue: number; // 平均值
  // 功率因数
  powerFactorMaxValue: number; // 最大值数值
  powerFactorMaxTime: string; // 最大值发生时间
  powerFactorMinValue: number; // 最小值数值
  powerFactorMinTime: string; // 最小值发生时间
  powerFactorAvgValue: number; // 平均值
}

/**
 * 电力极值报表表格列配置函数
 *
 * 该函数用于生成电力极值报表表格的列配置，包含复杂的多级表头结构：
 * - 回路名称、日期（固定列）
 * - 有功功率、无功功率、视在功率、功率因数（主列）
 * - 每个功率类型下包含最大值、最小值、平均值（子列）
 * - 最大值和最小值下又包含数值和发生时间（孙列）
 *
 * @param reportType - 报表类型，'dayly' 为日报，'monthly' 为月报，'custom' 为自定义
 * @param onActionClick - 可选的操作按钮点击回调函数
 *
 * @returns VxeTableGridOptions<PowerExtremesData>['columns']
 *   返回 VXE Table 表格的列配置数组，包含三级表头结构
 */
export function useColumns(
  reportType: ReportType = 'dayly',
  onActionClick?: OnActionClickFn<PowerExtremesData>,
): VxeTableGridOptions<PowerExtremesData>['columns'] {
  return [
    // 回路名称列 - 跨3行
    {
      field: 'circuitName',
      title: '回路名称',
      width: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
    // 日期列 - 跨3行
    {
      field: 'date',
      title: '日期',
      width: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
    // 有功功率(kW) - 主列
    {
      title: '有功功率(kW)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'activePowerMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'activePowerMaxTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'activePowerMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'activePowerMinTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'activePowerAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
    // 无功功率(kVar) - 主列
    {
      title: '无功功率(kVar)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'reactivePowerMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'reactivePowerMaxTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'reactivePowerMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'reactivePowerMinTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'reactivePowerAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
    // 视在功率(kVA) - 主列
    {
      title: '视在功率(kVA)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'apparentPowerMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'apparentPowerMaxTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'apparentPowerMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'apparentPowerMinTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'apparentPowerAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
    // 功率因数 - 主列
    {
      title: '功率因数',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'powerFactorMaxValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(3) || '0.000',
            },
            {
              field: 'powerFactorMaxTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'powerFactorMinValue',
              title: '数值',
              width: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(3) || '0.000',
            },
            {
              field: 'powerFactorMinTime',
              title: '发生时间',
              width: 120,
              align: 'center',
              headerAlign: 'center',
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'powerFactorAvgValue',
          title: '平均值',
          width: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(3) || '0.000',
        },
      ],
    },
  ];
}
