import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

// 报表类型定义
export type ReportType = 'custom' | 'dayly' | 'monthly';
/**
 * 电力极值报表表格列配置函数
 *
 * 该函数用于生成电力极值报表表格的列配置，包括时间列和五大时段列（尖、峰、平、谷、深谷），每个时段包含电量和成本两个子列。
 *
 * @param reportType - 报表类型，'monthly' 为月报，'yearly' 为年报
 * @param onActionClick - 可选的操作按钮点击回调函数
 *
 * @returns VxeTableGridOptions<ChargingIncomeReportData>['columns']
 *   返回 VXE Table 表格的列配置数组，包含以下列组：
 *   - 时间列: 固定在左侧的时间显示列（月报显示日期，年报显示月份）
 *   - 尖: 包含电量(kW·h)、成本(元)两个子列
 *   - 峰: 包含电量(kW·h)、成本(元)两个子列
 *   - 平: 包含电量(kW·h)、成本(元)两个子列
 *   - 谷: 包含电量(kW·h)、成本(元)两个子列
 *   - 深谷: 包含电量(kW·h)、成本(元)两个子列
 *
 * @example
 * ```typescript
 * // 月报列配置
 * const monthlyColumns = useColumns('monthly');
 *
 * // 年报列配置
 * const yearlyColumns = useColumns('yearly');
 * ```
 */

export function useColumns(
  reportType: ReportType = 'dayly',
  onActionClick?: OnActionClickFn<any>,
): VxeTableGridOptions<any>['columns'] {
  return [];
}
