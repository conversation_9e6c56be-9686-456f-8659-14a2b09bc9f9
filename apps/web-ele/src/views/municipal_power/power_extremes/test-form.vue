<script lang="ts" setup>
import { useVbenForm } from '#/adapter/form';
import { ElMessage } from 'element-plus';

// 测试表单配置 - 验证单个组件class设置
const [TestForm, testFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full', // 全局class - 基础宽度
    },
  },
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'testDate1',
      label: '日期选择器（对象式）',
      componentProps: {
        class: 'max-w-xs border-2 border-red-500', // 对象式class - 应该被覆盖
        placeholder: '请选择日期',
        type: 'date',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'testDate2',
      label: '日期选择器（函数式）',
      componentProps: (values, formApi) => ({
        class: 'max-w-sm border-2 border-blue-500', // 函数式class - 应该生效
        placeholder: '请选择日期',
        type: 'date',
      }),
    },
    {
      component: 'RangePicker',
      fieldName: 'testRange1',
      label: '范围选择器（函数式）',
      componentProps: (values, formApi) => ({
        class: 'max-w-md border-2 border-green-500', // 函数式class - 应该生效
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
      }),
    },
    {
      component: 'Input',
      fieldName: 'testInput',
      label: '输入框（全局class）',
      componentProps: {
        placeholder: '请输入内容',
      },
    },
  ],
  handleSubmit: (values) => {
    ElMessage.success(`表单数据：${JSON.stringify(values)}`);
  },
});
</script>

<template>
  <div class="p-4">
    <h2 class="text-lg font-bold mb-4">表单Class属性测试</h2>
    <div class="mb-4 p-4 bg-gray-100 rounded">
      <h3 class="font-semibold mb-2">测试说明：</h3>
      <ul class="list-disc list-inside space-y-1 text-sm">
        <li>全局配置：<code class="bg-white px-1 rounded">class: 'w-full'</code></li>
        <li><strong>对象式componentProps</strong>：class会被全局class覆盖（红色边框应该不显示）</li>
        <li><strong>函数式componentProps</strong>：class应该生效（蓝色和绿色边框应该显示）</li>
        <li>宽度对比：max-w-xs < max-w-sm < max-w-md < w-full</li>
      </ul>
    </div>

    <TestForm />

    <div class="mt-4 p-4 bg-yellow-50 rounded">
      <h3 class="font-semibold mb-2">预期结果：</h3>
      <ul class="list-disc list-inside space-y-1 text-sm">
        <li>第1个组件：只有全局class，无红色边框，宽度为w-full</li>
        <li>第2个组件：有蓝色边框，宽度为max-w-sm（较窄）</li>
        <li>第3个组件：有绿色边框，宽度为max-w-md（中等）</li>
        <li>第4个组件：只有全局class，宽度为w-full</li>
      </ul>
      <p class="text-sm mt-2"><strong>关键</strong>：函数式componentProps能够设置单个组件的class，对象式不行。</p>
    </div>
  </div>
</template>

<style scoped>
code {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}
</style>
