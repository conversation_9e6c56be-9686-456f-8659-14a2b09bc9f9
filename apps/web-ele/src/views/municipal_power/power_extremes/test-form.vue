<script lang="ts" setup>
import { useVbenForm } from '#/adapter/form';
import { ElMessage } from 'element-plus';

// 测试表单配置 - 验证class属性是否生效
const [TestForm, testFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full max-w-sm', // 全局class - 应该应用到所有组件
    },
  },
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'testDate1',
      label: '日期选择器',
      componentProps: {
        placeholder: '请选择日期',
        type: 'date',
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'testRange1',
      label: '日期范围选择器',
      componentProps: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
      },
    },
    {
      component: 'Input',
      fieldName: 'testInput',
      label: '输入框（对比）',
      componentProps: {
        placeholder: '请输入内容',
      },
    },
  ],
  handleSubmit: (values) => {
    ElMessage.success(`表单数据：${JSON.stringify(values)}`);
  },
});
</script>

<template>
  <div class="p-4">
    <h2 class="text-lg font-bold mb-4">表单Class属性测试</h2>
    <div class="mb-4 p-4 bg-gray-100 rounded">
      <h3 class="font-semibold mb-2">测试说明：</h3>
      <ul class="list-disc list-inside space-y-1 text-sm">
        <li>全局配置：<code class="bg-white px-1 rounded">class: 'w-full max-w-sm'</code></li>
        <li>所有组件都应该应用这个class，宽度应该被限制为max-w-sm</li>
        <li>如果class生效，组件宽度应该比较窄（max-w-sm = 24rem）</li>
        <li>如果class无效，组件会占满整个容器宽度</li>
      </ul>
    </div>

    <TestForm />

    <div class="mt-4 p-4 bg-yellow-50 rounded">
      <h3 class="font-semibold mb-2">观察结果：</h3>
      <p class="text-sm">请检查上面的表单组件宽度，如果class生效，组件应该比较窄；如果无效，组件会占满整个宽度。</p>
      <p class="text-sm mt-2">可以通过浏览器开发者工具检查组件的实际class属性。</p>
    </div>
  </div>
</template>

<style scoped>
code {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}
</style>
