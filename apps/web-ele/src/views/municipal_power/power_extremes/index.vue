<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { ColPage } from '@vben/common-ui';
import type { VbenFormProps } from '@vben-core/form-ui';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { IconifyIcon } from '@vben/icons';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ElButton, ElTooltip, ElTabPane, ElTabs } from 'element-plus';
import { useColumns } from './data';
import type { ReportType } from './data';
import dayjs from 'dayjs';
import { $t } from '@vben/locales';

const props = reactive({
  leftCollapsedWidth: 5,
  leftCollapsible: false, // 左侧可折叠
  leftMaxWidth: 30, // 左侧最大宽度百分比
  leftMinWidth: 30, // 左侧最小宽度百分比
  leftWidth: 30, // 左侧初始宽度百分比
  resizable: true, // 可拖动调整宽度
  rightWidth: 70,
  splitHandle: false, // 显示拖动手柄
  splitLine: false, // 显示拖动分隔线
});

// 当前激活的标签页
const activeTab = ref('dayly');

// 创建动态表单配置函数
function createFormOptions(reportType: ReportType): VbenFormProps {
  const isMonthly = reportType === 'monthly';
  return {
    // 默认展开搜索表单
    collapsed: false,
    // 所有表单项共用配置
    commonConfig: {
      // 所有表单项统一样式
      componentProps: {
        class: 'w-full',
      },
    },
    // 字段映射时间 - 根据报表类型调整格式
    fieldMappingTime: [
      ['time', ['startTime', 'endTime'], isMonthly ? 'YYYY-MM-DD' : 'YYYY-MM'],
    ],
    schema: [
      {
        component: 'RangePicker',
        // 根据报表类型设置默认值
        defaultValue: isMonthly
          ? [dayjs().subtract(7, 'days'), dayjs()]
          : [
              dayjs().subtract(11, 'months').startOf('month'),
              dayjs().endOf('month'),
            ],
        fieldName: 'time',
        label: isMonthly
          ? $t('system.chargingIncomeReport.form.dateRange')
          : $t('system.chargingIncomeReport.form.monthRange'),
        componentProps: {
          class: 'w-full',
          startPlaceholder: isMonthly
            ? $t('system.chargingIncomeReport.form.startDate')
            : $t('system.chargingIncomeReport.form.startMonth'),
          endPlaceholder: isMonthly
            ? $t('system.chargingIncomeReport.form.endDate')
            : $t('system.chargingIncomeReport.form.endMonth'),
          rangeSeparator: $t('system.chargingIncomeReport.form.separator'),
          format: isMonthly ? 'YYYY-MM-DD' : 'YYYY-MM',
          valueFormat: isMonthly ? 'YYYY-MM-DD' : 'YYYY-MM',
          clearable: true,
          size: 'default',
          // 年报使用月份选择器
          type: isMonthly ? 'daterange' : 'monthrange',
        },
      },
    ],
    // 控制表单是否显示折叠按钮
    showCollapseButton: false,
    // 是否在字段值改变时提交表单
    submitOnChange: true,
    // 按下回车时是否提交表单
    submitOnEnter: false,
    // 表单布局
    layout: 'horizontal',
  };
}

// 创建基础表格配置
const createGridOptions = (reportType: ReportType) =>
  ({
    columns: useColumns(reportType),
    height: 'auto',
    keepSource: true,
    showFooter: true, // 开启表尾显示
    // 表格自适应配置
    resizable: true, // 允许调整列宽
    autoResize: true, // 自动调整大小
    columnConfig: {
      resizable: true, // 列可调整大小
      useKey: true, // 使用key优化性能
    },
    // 表格布局配置
    border: true, // 显示边框
    stripe: true, // 斑马纹
    round: true, // 圆角
    pagerConfig: {
      enabled: false,
    },
    rowConfig: {
      keyField: 'time',
    },
    toolbarConfig: {
      search: false,
      custom: true,
      export: true,
      refresh: { code: 'query' },
      zoom: true,
    },
  }) as VxeTableGridOptions<any>;

// 日报表格
const [DaylyGrid, dayGridApi] = useVbenVxeGrid({
  formOptions: createGridOptions('dayly'),
  showSearchForm: true,
  gridOptions: createGridOptions('dayly'),
});
// 月报表格
const [MonthlyGrid, monthlyGridApi] = useVbenVxeGrid({
  formOptions: createGridOptions('monthly'),
  showSearchForm: true,
  gridOptions: createGridOptions('monthly'),
});
// 自定义报表表格
const [CustomGrid, customGridApi] = useVbenVxeGrid({
  formOptions: createGridOptions('custom'),
  showSearchForm: true,
  gridOptions: createGridOptions('custom'),
});

function onRefresh() {
  if (activeTab.value === 'day') {
    dayGridApi.query();
  } else if (activeTab.value === 'monthly') {
    monthlyGridApi.query();
  } else {
    customGridApi.query();
  }
}
</script>
<template>
  <ColPage auto-content-height v-bind="props">
    <template #left="{ isCollapsed, expand, collapse }">
      <div
        class="bg-card border-border mr-2 h-full rounded-[var(--radius)] border"
      >
        <div
          v-if="isCollapsed"
          @click="expand"
          class="flex h-full items-center justify-center"
        >
          <ElTooltip content="点击展开左侧">
            <ElButton size="small" text @click="expand">
              <IconifyIcon
                icon="mdi:chevron-right"
                class="text-xl md:text-[32px]"
              />
            </ElButton>
          </ElTooltip>
        </div>
      </div>
    </template>
    <div class="h-full">
      <ElTabs v-model="activeTab" type="border-card" class="report-tabs">
        <ElTabPane label="日报" name="dayly">
          <DaylyGrid />
        </ElTabPane>
        <ElTabPane label="月报" name="monthly">
          <MonthlyGrid />
        </ElTabPane>
        <ElTabPane label="自定义" name="custom">
          <CustomGrid />
        </ElTabPane>
      </ElTabs>
    </div>
  </ColPage>
</template>

<style scoped lang="scss">
// ElTabs 样式优化
.report-tabs {
  @apply h-full;

  // 标签页导航样式
  :deep(.el-tabs__nav-wrap) {
    @apply bg-background border-border border-r;
  }

  :deep(.el-tabs__nav) {
    @apply bg-background;
  }

  :deep(.el-tabs__item) {
    @apply text-foreground transition-colors duration-200;

    &:hover {
      @apply text-primary;
    }

    &.is-active {
      @apply text-primary font-medium;
    }
  }

  // 标签页内容区域
  :deep(.el-tabs__content) {
    @apply h-full overflow-hidden;
  }

  :deep(.el-tab-pane) {
    @apply h-full;
  }
}

// 表单区域样式优化
:deep(.vben-form) {
  .vben-form-item {
    @apply mb-4;
  }

  .vben-form-label {
    @apply text-foreground font-medium;
  }
}

// 工具栏按钮样式
:deep(.vxe-toolbar) {
  .vben-button {
    @apply transition-all duration-200;

    &:hover {
      @apply shadow-md;
    }
  }
}

// VXE Table 样式优化 - 遵循设计系统
:deep(.vxe-table) {
  // 边框合并设置
  border-collapse: collapse;
  @apply border-border overflow-hidden rounded-md border;

  // 表头样式 - 恢复原来的设置并遵循设计系统
  .vxe-header--column {
    //background-color: hsl(var(--primary) / 0.15) !important;
    border-right: 1px solid var(--vxe-ui-table-header-border-color);
    border-bottom: 1px solid var(--vxe-ui-table-header-border-color);
    font-weight: 600;
    color: inherit !important;
  }

  // 深色模式下的表头背景色
  .dark .vxe-header--column {
    background-color: hsl(var(--accent-foreground)) !important;
  }

  // 覆盖VXE Table的默认表头字体颜色，让其遵循设计系统
  .vxe-table--header-wrapper {
    color: inherit !important;
  }

  .vxe-header--column {
    color: inherit !important;
  }

  // 数据行边框使用设计系统颜色
  .vxe-body--column {
    border-right: 1px solid var(--vxe-ui-table-border-color);
    border-bottom: 1px solid var(--vxe-ui-table-border-color);
  }

  // 悬停效果
  .vxe-body--row:hover {
    //background-color: hsl(var(--accent) / 0.1);
  }

  // 表尾合计行样式 - 已在全局样式中设置
  // .vxe-footer--column {
  //   background-color: hsl(var(--muted));
  //   font-weight: 600;
  //   border-right: 1px solid var(--vxe-ui-table-border-color);
  // }

  // 只对真正的最后一列（表格最右侧）移除右边框
  .vxe-table .vxe-header--column:last-child,
  .vxe-table .vxe-body--column:last-child {
    border-right: none;
  }

  // 确保所有表头列都有白色右边框，除非是表格的最后一列
  .vxe-header--column {
    //border-right: 1px solid white !important;
  }

  // 只对表格最右侧的列移除右边框
  .vxe-table
    > .vxe-table--main-wrapper
    > .vxe-table--header-wrapper
    .vxe-header--column:last-child,
  .vxe-table
    > .vxe-table--main-wrapper
    > .vxe-table--body-wrapper
    .vxe-body--column:last-child {
    border-right: none !important;
  }

  .vxe-body--row:last-child .vxe-body--column {
    border-bottom: none;
  }

  // 隐藏列宽调整的伪元素分隔线，确保边框纯色
  .vxe-cell--col-resizable:before {
    display: none !important;
  }

  .vxe-table--render-default.border--default .vxe-cell--col-resizable:before,
  .vxe-table--render-default.border--none .vxe-cell--col-resizable:before,
  .vxe-table--render-default.border--outer .vxe-cell--col-resizable:before,
  .vxe-table--render-default.border--inner .vxe-cell--col-resizable:before {
    display: none !important;
  }

  // 表格内容文字样式
  .vxe-cell {
    @apply text-foreground;
  }

  // 数字列右对齐
  .vxe-cell--number {
    @apply text-right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.vxe-table) {
    font-size: 12px;
  }

  :deep(.vben-form) {
    .vben-form-item {
      @apply mb-3;
    }
  }
}
</style>
