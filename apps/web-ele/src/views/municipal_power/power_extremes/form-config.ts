import type { VbenFormProps } from '@vben-core/form-ui';
import dayjs from 'dayjs';
import { $t } from '@vben/locales';
import type { ReportType } from './data';

/**
 * 电力极值报表表单配置
 * 根据不同的报表类型返回对应的表单schema配置
 */

/**
 * 日报表单配置
 * 使用单个日期选择器
 */
export function getDaylyFormSchema(): VbenFormProps['schema'] {
  return [
    {
      component: 'DatePicker',
      defaultValue: dayjs().format('YYYY-MM-DD'),
      fieldName: 'date',
      label: '查询日期',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm',
        placeholder: '请选择查询日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        clearable: true,
        size: 'default',
        type: 'date',
      },
    },
  ];
}

/**
 * 月报表单配置
 * 使用月份选择器
 */
export function getMonthlyFormSchema(): VbenFormProps['schema'] {
  return [
    {
      component: 'DatePicker',
      defaultValue: dayjs().format('YYYY-MM'),
      fieldName: 'month',
      label: '查询月份',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm',
        placeholder: '请选择查询月份',
        format: 'YYYY-MM',
        valueFormat: 'YYYY-MM',
        clearable: true,
        size: 'default',
        type: 'month',
      },
    },
  ];
}

/**
 * 自定义报表表单配置
 * 使用日期范围选择器
 */
export function getCustomFormSchema(): VbenFormProps['schema'] {
  return [
    {
      component: 'RangePicker',
      defaultValue: [
        dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      fieldName: 'dateRange',
      label: '查询时间范围',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        rangeSeparator: '至',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        clearable: true,
        size: 'default',
        type: 'daterange',
      },
    },
  ];
}

/**
 * 根据报表类型获取对应的表单schema配置
 * @param reportType 报表类型
 * @returns 表单schema配置数组
 */
export function getFormSchemaByType(
  reportType: ReportType,
): VbenFormProps['schema'] {
  switch (reportType) {
    case 'dayly':
      return getDaylyFormSchema();
    case 'monthly':
      return getMonthlyFormSchema();
    case 'custom':
      return getCustomFormSchema();
    default:
      return getDaylyFormSchema();
  }
}

/**
 * 创建完整的表单配置
 * @param reportType 报表类型
 * @returns 完整的表单配置对象
 */
export function createFormOptions(reportType: ReportType): VbenFormProps {
  return {
    // 默认展开搜索表单
    collapsed: false,
    // 所有表单项共用配置
    commonConfig: {
      // 所有表单项统一样式
      componentProps: {
        class: 'w-full',
      },
    },
    // 根据报表类型获取对应的schema配置
    schema: getFormSchemaByType(reportType),
    // 控制表单是否显示折叠按钮
    showCollapseButton: false,
    // 是否在字段值改变时提交表单
    submitOnChange: true,
    // 按下回车时是否提交表单
    submitOnEnter: false,
    // 表单布局
    layout: 'horizontal',
  };
}
