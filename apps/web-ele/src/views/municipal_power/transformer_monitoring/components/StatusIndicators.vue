<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

// 为每个图表创建独立的ref和useEcharts实例
const chart1Ref = ref<EchartsUIType>();
const chart2Ref = ref<EchartsUIType>();
const chart3Ref = ref<EchartsUIType>();
const chart4Ref = ref<EchartsUIType>();

const { renderEcharts: renderChart1 } = useEcharts(chart1Ref);
const { renderEcharts: renderChart2 } = useEcharts(chart2Ref);
const { renderEcharts: renderChart3 } = useEcharts(chart3Ref);
const { renderEcharts: renderChart4 } = useEcharts(chart4Ref);

// 创建仪表盘配置的函数
const createGaugeOption = (
  value: number,
  unit: string,
  color: string,
  max: number = 100,
) => ({
  series: [
    {
      type: 'gauge',
      center: ['50%', '50%'],
      radius: '70%',
      min: 0,
      max,
      splitNumber: 4, // 减少分割线数量
      progress: {
        show: true,
        width: 8,
        itemStyle: {
          color: color,
        },
      },
      axisLine: {
        lineStyle: {
          width: 8,
          color: [
            [1, '#e6e6e6'], // 背景轨道颜色
          ],
        },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        length: 10,
        lineStyle: {
          width: 1,
          color: '#666',
        },
      },
      axisLabel: {
        distance: 20,
        color: '#999',
        fontSize: 10,
        formatter: function(value: number) {
          return Math.round(value).toString(); // 显示整数
        },
      },
      anchor: {
        show: true,
        showAbove: true,
        size: 8, // 进一步缩小锚点
        itemStyle: {
          borderWidth: 2,
          borderColor: color,
          color: color,
        },
      },
      title: {
        show: false,
      },
      detail: {
        valueAnimation: true,
        fontSize: 20,
        offsetCenter: [0, '60%'], // 调整数值显示位置
        formatter: `{value}${unit}`,
        color: 'inherit',
        fontWeight: 'bold',
      },
      data: [
        {
          value,
        },
      ],
    },
  ],
});

onMounted(() => {
  // 频率
  renderChart1(createGaugeOption(50, 'Hz', '#67e0e3', 60));

  // 功率因数
  renderChart2(createGaugeOption(0.98, '', '#ff7f50', 1));

  // 三相电压不平衡度
  renderChart3(createGaugeOption(0, '', '#37a2da', 100));

  // 三相电流不平衡度
  renderChart4(createGaugeOption(0, '', '#32cd32', 100));
});
</script>

<template>
  <div class="grid h-full grid-cols-4 gap-4">
    <!-- 频率 -->
    <div class="flex flex-col items-center">
      <div class="w-full flex-1">
        <EchartsUI ref="chart1Ref" height="100%" />
      </div>
      <div class="mt-2 text-center">
        <div class="text-foreground text-sm font-medium">频率</div>
      </div>
    </div>

    <!-- 功率因数 -->
    <div class="flex flex-col items-center">
      <div class="w-full flex-1">
        <EchartsUI ref="chart2Ref" height="100%" />
      </div>
      <div class="mt-2 text-center">
        <div class="text-foreground text-sm font-medium">功率因数</div>
      </div>
    </div>

    <!-- 三相电压不平衡度 -->
    <div class="flex flex-col items-center">
      <div class="w-full flex-1">
        <EchartsUI ref="chart3Ref" height="100%" />
      </div>
      <div class="mt-2 text-center">
        <div class="text-foreground text-sm font-medium">三相电压不平衡度</div>
      </div>
    </div>

    <!-- 三相电流不平衡度 -->
    <div class="flex flex-col items-center">
      <div class="w-full flex-1">
        <EchartsUI ref="chart4Ref" height="100%" />
      </div>
      <div class="mt-2 text-center">
        <div class="text-foreground text-sm font-medium">三相电流不平衡度</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.grid-cols-4 > div {
  min-height: 0;
}
</style>
